{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:14,113+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:14,293+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:14,294+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:18,900+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:18,933+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:19,208+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:22,383+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:22,384+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:26,587+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:26,589+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:26,592+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:31,906+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:31,907+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:32,110+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:36,348+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:36,393+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:36,422+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:36,429+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:36,490+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:42,894+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:42,965+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:46,329+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:46,450+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:46,512+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:48,903+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:49,007+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:49,518+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:49,623+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:49,676+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:52,084+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:52,237+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:52,691+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:52,795+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:52,840+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:55,213+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:55,399+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:55,855+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:55,939+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:55,985+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:58,529+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:58,885+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:59,059+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:59,059+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:18:59,657+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:01,634+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:02,013+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:02,320+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:02,729+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:02,831+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:05,185+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:05,397+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:05,596+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:05,985+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:06,406+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:08,496+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:08,771+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:08,875+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:09,136+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:10,101+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:11,637+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:12,047+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:12,458+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:12,461+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:13,494+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:15,489+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:15,493+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:16,056+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:16,057+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:16,656+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:18,604+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:19,171+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:19,177+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:22,877+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:22,975+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:23,108+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:23,108+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:27,008+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:27,102+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:27,110+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 20:19:30,480+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:05:52,110+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:05:52,110+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:05:52,208+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:05:52,208+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:05:52,387+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:05:56,227+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:05:58,159+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:05:58,176+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:05:58,273+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:05:58,373+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:02,503+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:02,509+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:02,509+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:02,637+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:02,856+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:07,679+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:07,744+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:07,744+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:07,881+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:07,881+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:11,655+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:11,655+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:11,774+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:11,786+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:11,871+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:15,465+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:15,525+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:15,533+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:15,772+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:19,295+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:19,304+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:22,616+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:22,622+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:25,824+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:25,886+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:25,897+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:29,780+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:29,780+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:29,896+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:29,966+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:33,385+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:33,446+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:33,547+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:33,639+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:33,639+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:37,442+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:37,468+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:37,578+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:37,578+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:41,505+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:41,555+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:41,675+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:41,775+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:44,233+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:44,701+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:44,808+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:44,948+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:45,258+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:47,508+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:47,925+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:47,944+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:48,126+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:48,687+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:50,787+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:51,097+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:51,097+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:51,298+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:51,900+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:54,064+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:54,325+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:54,365+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:54,473+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:55,189+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:57,238+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:57,490+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:57,490+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:57,598+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:06:58,365+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:00,723+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:00,822+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:01,030+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:01,334+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:01,745+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:03,921+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:04,048+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:04,203+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:04,505+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:04,921+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:08,049+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:08,749+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:08,932+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:09,016+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:09,081+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:12,740+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:12,805+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:12,913+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:13,011+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:13,011+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:16,524+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:16,659+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:16,807+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:16,903+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:16,903+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:20,404+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:20,513+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:20,587+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:20,689+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:24,377+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:24,502+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:24,579+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:24,579+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:28,080+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:28,139+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:28,139+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:28,373+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:28,373+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:31,963+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:32,055+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:32,121+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:32,264+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:32,264+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:35,795+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:35,860+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:35,922+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:35,924+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:36,009+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:39,627+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:39,739+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:39,739+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:39,861+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:43,346+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:43,423+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:43,431+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:47,416+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:47,523+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:47,523+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:47,660+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:51,217+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:51,389+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:51,397+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:51,448+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:51,515+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:54,384+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:54,571+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:54,579+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:54,692+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:54,692+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:57,603+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:57,719+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:57,719+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:57,909+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:07:57,923+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:00,820+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:00,938+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:00,943+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:01,038+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:01,135+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:03,990+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:04,120+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:04,128+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:04,169+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:04,313+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:07,899+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:08,000+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:08,000+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:11,548+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:11,585+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:13,736+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:15,896+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:15,908+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:15,958+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:19,977+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:20,143+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:20,291+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:20,293+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:20,333+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:24,182+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:24,270+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:24,270+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:24,330+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:28,584+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:28,633+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:28,651+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:28,742+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:28,886+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:32,540+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:32,591+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:32,653+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:32,653+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:36,254+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:36,368+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:36,368+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:36,380+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:36,470+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:40,265+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:40,269+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:40,360+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:40,436+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:42,720+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:43,433+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:43,437+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:43,494+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:43,638+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:45,947+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:46,552+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:46,613+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:46,613+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:47,433+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:49,169+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:49,785+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:49,990+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:50,433+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:50,705+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:52,295+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:52,957+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:53,163+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:53,676+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:53,978+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:56,192+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:56,338+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:56,539+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:56,851+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:57,133+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:59,356+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:59,506+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:08:59,718+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:00,025+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:00,390+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:02,562+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:02,687+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:02,886+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:03,236+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:03,573+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:05,708+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:05,859+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:06,047+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:06,369+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:06,784+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:10,065+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:10,074+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:14,157+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:14,165+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:14,165+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:14,285+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:14,421+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:18,166+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:18,166+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:21,712+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:21,712+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:25,728+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:25,796+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:25,836+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:25,960+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:29,513+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:29,638+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:29,657+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:29,721+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:29,797+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:33,394+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:33,435+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:33,493+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:33,615+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:37,128+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:37,271+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:37,304+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:40,385+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:40,417+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:40,450+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:40,575+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:40,575+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:43,754+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:43,758+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:43,856+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:43,960+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:43,964+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:46,877+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:46,926+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:47,133+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:47,133+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:47,646+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:50,100+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:50,100+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:50,361+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:50,409+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:50,820+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:53,274+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:53,274+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:53,509+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:53,585+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:53,994+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:56,452+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:56,505+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:56,654+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:56,863+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:57,269+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:59,733+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:09:59,831+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:00,143+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:00,240+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:00,493+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:02,928+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:03,004+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:03,301+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:03,366+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:03,793+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:06,177+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:06,228+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:06,484+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:07,105+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:07,105+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:11,076+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:11,083+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:11,199+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:11,199+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:11,301+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:14,937+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:15,027+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:15,067+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:15,186+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:15,295+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:18,950+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:18,954+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:18,962+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:19,090+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:22,562+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:26,012+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:26,852+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:26,852+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:27,017+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:27,024+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:30,928+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:31,062+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:31,069+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:31,069+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:31,170+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:34,708+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:34,763+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:34,801+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:34,856+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:34,856+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:38,844+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:38,944+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:38,944+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:38,956+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:38,975+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:42,643+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:42,643+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:42,945+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:42,945+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:47,001+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:47,009+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:47,034+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:47,074+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:47,256+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:50,780+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:50,920+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:51,137+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:52,676+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:52,876+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:53,942+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:54,167+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:54,290+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:55,952+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:56,075+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:57,281+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:57,387+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:57,494+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:59,152+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:10:59,222+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:00,456+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:00,660+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:00,660+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:02,279+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:02,401+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:03,632+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:03,939+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:03,939+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:05,429+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:05,579+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:06,806+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:07,190+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:07,190+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:08,664+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-07 21:11:08,852+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:30:02,626+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:30:05,754+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:31:05,516+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:31:08,627+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:32:08,545+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:32:11,668+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:33:16,756+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:34:13,977+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:34:17,109+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:35:14,764+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:35:17,907+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:36:16,534+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-07 23:36:19,754+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:00:08,798+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:00:11,928+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:01:13,450+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:01:16,613+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:02:13,624+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:02:17,107+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:03:17,483+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:03:20,604+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:04:18,258+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:04:21,427+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:05:21,151+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:05:24,285+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:06:23,743+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:06:26,921+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:50:00,212+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:50:03,326+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:51:04,615+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:51:07,757+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:52:10,782+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:53:09,318+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:53:12,454+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:54:11,647+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:54:14,785+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:55:13,272+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:55:16,427+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:56:18,912+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 00:56:22,051+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:00:54,187+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:00:57,358+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:02:01,695+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:04:58,636+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:05:02,714+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:06:04,707+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:06:07,928+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:08:47,343+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:08:50,490+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:09:50,255+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:09:53,410+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:10:51,031+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:10:54,220+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:11:49,237+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:11:53,023+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:11:59,126+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:13:03,336+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:13:06,462+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:14:00,440+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:14:03,638+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:14:09,763+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:15:10,490+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:15:13,621+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 22:15:19,732+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:27:20,276+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:27:23,415+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:28:20,149+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:28:23,649+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:29:14,213+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:29:14,422+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:29:17,364+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:29:17,542+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:29:23,476+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:29:23,826+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:30:01,796+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:30:01,860+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:30:04,972+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:30:05,105+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:30:11,155+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:30:11,345+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:30:23,273+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:30:23,470+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:31:11,895+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:31:11,897+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:31:15,421+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:31:15,421+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:31:21,552+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:31:21,554+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:31:33,666+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:31:33,744+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:32:22,373+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:32:22,373+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:32:25,491+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:32:25,518+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:32:31,612+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:32:31,627+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:32:43,780+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:32:43,785+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:33:34,737+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:33:34,773+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:33:38,119+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:33:38,119+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:33:44,231+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:33:44,234+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:33:56,386+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:33:56,386+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:34:46,173+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:34:46,303+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:34:49,316+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:34:49,417+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:34:55,461+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:34:55,582+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:35:07,600+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:35:07,694+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:35:59,053+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:35:59,329+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:36:02,186+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:36:02,466+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:36:08,319+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:36:08,575+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:36:20,451+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:36:21,090+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:37:11,231+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:37:11,575+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:37:14,573+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:37:15,223+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:37:20,709+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:37:21,337+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:37:32,980+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:37:33,740+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:38:23,290+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:38:23,517+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:38:26,477+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:38:26,662+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:38:32,577+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:38:32,798+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:38:44,701+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:38:44,911+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:39:36,415+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:39:39,570+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:39:45,695+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:39:57,833+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:41:16,137+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:41:19,281+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:42:20,750+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-08 23:42:23,893+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:00:03,393+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:00:06,514+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:01:11,889+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:02:06,176+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:02:09,391+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:03:07,973+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:03:11,092+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:04:10,990+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:04:14,097+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:05:09,892+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:05:10,068+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:05:13,010+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:05:13,209+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:05:19,196+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:05:19,325+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:12:41,679+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:13:36,692+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:13:39,806+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:14:34,975+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:14:38,106+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:14:45,204+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:15:46,414+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:15:49,699+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:15:55,828+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:17:00,655+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:17:04,236+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:17:58,482+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:18:01,628+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:18:07,740+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:19:10,756+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:19:13,891+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:39:59,976+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:40:03,088+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:40:59,843+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:41:02,955+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:41:09,091+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:42:12,381+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:42:15,496+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:52:06,385+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:52:09,516+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:53:05,315+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:53:08,444+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:53:14,602+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:54:19,525+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 00:54:22,702+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 02:02:53,963+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 02:02:57,591+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 02:03:57,098+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/quotes","Error":{"message":"request limit reached","code":429,"s":"error"}},"timestamp":"2025-07-09 02:04:00,210+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 14:18:27,961+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 14:18:31,096+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 14:18:34,418+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 14:19:29,419+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 14:19:32,539+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 14:19:35,674+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:19:57,781+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:20:00,917+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:20:55,451+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:20:58,651+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:21:01,807+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:21:57,934+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:22:01,095+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:22:04,196+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:22:56,376+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:22:59,493+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:23:03,108+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:23:06,215+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:23:57,398+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:24:00,501+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:24:03,615+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:24:06,758+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:55:06,131+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:55:09,232+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:56:05,118+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:56:08,278+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:56:11,386+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:57:05,454+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:57:08,917+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:57:12,037+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:58:04,500+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:58:07,631+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:58:10,795+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:58:13,895+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-09 15:59:16,936+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-300,"message":"Invalid symbol provided","s":"error"}},"timestamp":"2025-07-13 02:06:01,236+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-300,"message":"Invalid symbol provided","s":"error"}},"timestamp":"2025-07-13 02:06:01,847+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-300,"message":"Invalid symbol provided","s":"error"}},"timestamp":"2025-07-13 02:13:03,439+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:146] fyersModel","message":{"API":"/history","Error":{"code":-300,"message":"Invalid symbol provided","s":"error"}},"timestamp":"2025-07-13 02:13:03,874+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))","timestamp":"2025-07-18 16:18:48,404+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Read timed out. (read timeout=None)","timestamp":"2025-07-18 21:06:06,483+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))","timestamp":"2025-07-19 00:44:16,268+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))","timestamp":"2025-07-19 00:44:42,590+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))","timestamp":"2025-07-20 10:26:43,936+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))","timestamp":"2025-07-20 10:32:21,454+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Read timed out. (read timeout=None)","timestamp":"2025-07-20 10:40:24,004+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))","timestamp":"2025-07-20 10:42:28,530+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))","timestamp":"2025-07-20 10:50:15,207+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 16:56:51,964+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 16:56:55,129+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 16:57:01,776+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/quotes?symbols=NSE%3ARELIANCE-EQ (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000020EF4D32E30>, 'Connection to api-t1.fyers.in timed out. (connect timeout=None)'))","timestamp":"2025-07-23 16:57:55,847+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/quotes?symbols=NSE%3ARELIANCE-EQ (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x0000020EF4E259F0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-23 16:57:55,889+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/quotes?symbols=NSE%3ARELIANCE-EQ (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x0000020EF4E4D5A0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-23 16:57:55,939+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/quotes?symbols=NSE%3ASYMBOL0-EQ%2CNSE%3ASYMBOL1-EQ%2CNSE%3ASYMBOL2-EQ%2CNSE%3ASYMBOL3-EQ%2CNSE%3ASYMBOL4-EQ%2CNSE%3ASYMBOL5-EQ%2CNSE%3ASYMBOL6-EQ%2CNSE%3ASYMBOL7-EQ%2CNSE%3ASYMBOL8-EQ%2CNSE%3ASYMBOL9-EQ%2CNSE%3ASYMBOL10-EQ%2CNSE%3ASYMBOL11-EQ%2CNSE%3ASYMBOL12-EQ%2CNSE%3ASYMBOL13-EQ%2CNSE%3ASYMBOL14-EQ%2CNSE%3ASYMBOL15-EQ%2CNSE%3ASYMBOL16-EQ%2CNSE%3ASYMBOL17-EQ%2CNSE%3ASYMBOL18-EQ%2CNSE%3ASYMBOL19-EQ%2CNSE%3ASYMBOL20-EQ%2CNSE%3ASYMBOL21-EQ%2CNSE%3ASYMBOL22-EQ%2CNSE%3ASYMBOL23-EQ%2CNSE%3ASYMBOL24-EQ%2CNSE%3ASYMBOL25-EQ%2CNSE%3ASYMBOL26-EQ%2CNSE%3ASYMBOL27-EQ%2CNSE%3ASYMBOL28-EQ%2CNSE%3ASYMBOL29-EQ%2CNSE%3ASYMBOL30-EQ%2CNSE%3ASYMBOL31-EQ%2CNSE%3ASYMBOL32-EQ%2CNSE%3ASYMBOL33-EQ%2CNSE%3ASYMBOL34-EQ%2CNSE%3ASYMBOL35-EQ%2CNSE%3ASYMBOL36-EQ%2CNSE%3ASYMBOL37-EQ%2CNSE%3ASYMBOL38-EQ%2CNSE%3ASYMBOL39-EQ%2CNSE%3ASYMBOL40-EQ%2CNSE%3ASYMBOL41-EQ%2CNSE%3ASYMBOL42-EQ%2CNSE%3ASYMBOL43-EQ%2CNSE%3ASYMBOL44-EQ%2CNSE%3ASYMBOL45-EQ%2CNSE%3ASYMBOL46-EQ%2CNSE%3ASYMBOL47-EQ%2CNSE%3ASYMBOL48-EQ%2CNSE%3ASYMBOL49-EQ (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x0000020EF4E24100>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-23 16:57:55,961+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/quotes?symbols=NSE%3ASYMBOL50-EQ%2CNSE%3ASYMBOL51-EQ%2CNSE%3ASYMBOL52-EQ%2CNSE%3ASYMBOL53-EQ%2CNSE%3ASYMBOL54-EQ%2CNSE%3ASYMBOL55-EQ%2CNSE%3ASYMBOL56-EQ%2CNSE%3ASYMBOL57-EQ%2CNSE%3ASYMBOL58-EQ%2CNSE%3ASYMBOL59-EQ%2CNSE%3ASYMBOL60-EQ%2CNSE%3ASYMBOL61-EQ%2CNSE%3ASYMBOL62-EQ%2CNSE%3ASYMBOL63-EQ%2CNSE%3ASYMBOL64-EQ%2CNSE%3ASYMBOL65-EQ%2CNSE%3ASYMBOL66-EQ%2CNSE%3ASYMBOL67-EQ%2CNSE%3ASYMBOL68-EQ%2CNSE%3ASYMBOL69-EQ%2CNSE%3ASYMBOL70-EQ%2CNSE%3ASYMBOL71-EQ%2CNSE%3ASYMBOL72-EQ%2CNSE%3ASYMBOL73-EQ%2CNSE%3ASYMBOL74-EQ%2CNSE%3ASYMBOL75-EQ%2CNSE%3ASYMBOL76-EQ%2CNSE%3ASYMBOL77-EQ%2CNSE%3ASYMBOL78-EQ%2CNSE%3ASYMBOL79-EQ%2CNSE%3ASYMBOL80-EQ%2CNSE%3ASYMBOL81-EQ%2CNSE%3ASYMBOL82-EQ%2CNSE%3ASYMBOL83-EQ%2CNSE%3ASYMBOL84-EQ%2CNSE%3ASYMBOL85-EQ%2CNSE%3ASYMBOL86-EQ%2CNSE%3ASYMBOL87-EQ%2CNSE%3ASYMBOL88-EQ%2CNSE%3ASYMBOL89-EQ%2CNSE%3ASYMBOL90-EQ%2CNSE%3ASYMBOL91-EQ%2CNSE%3ASYMBOL92-EQ%2CNSE%3ASYMBOL93-EQ%2CNSE%3ASYMBOL94-EQ%2CNSE%3ASYMBOL95-EQ%2CNSE%3ASYMBOL96-EQ%2CNSE%3ASYMBOL97-EQ%2CNSE%3ASYMBOL98-EQ%2CNSE%3ASYMBOL99-EQ (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x0000020EF4E27520>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-23 16:57:55,967+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/quotes?symbols=NSE%3ARELIANCE-EQ (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x0000020EF4EBB2B0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-23 16:57:56,000+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /api/v3/profile (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x0000020EF4E25570>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-23 16:57:56,046+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:01:04,881+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:01:08,480+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:01:15,077+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:01:28,211+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:01:52,787+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:02:41,385+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:02:42,017+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:02:45,158+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:02:51,746+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:03:03,844+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:03:29,048+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:43:56,984+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:44:00,788+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:44:07,745+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:44:22,761+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:44:47,579+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:45:36,315+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:45:38,027+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:45:41,238+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:45:47,990+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:46:00,561+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:46:24,770+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:47:13,594+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:47:15,566+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:47:18,657+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:47:25,832+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:47:38,002+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:48:02,756+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:48:51,711+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:48:53,112+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:48:54,697+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:48:56,270+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:48:57,862+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:48:59,694+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:49:01,425+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:49:03,374+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:49:05,315+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"Expecting value: line 1 column 1 (char 0)","timestamp":"2025-07-23 17:49:06,414+0530","service":"FyersDataSocket"}
